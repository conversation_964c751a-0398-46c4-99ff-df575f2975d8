; Assembly stub and BIOS interface for example_kernel.c
[BITS 16]

; External C function
extern kernel_main

; Entry point
global _start
_start:
    ; Segment setup
    mov ax, 0x8000
    mov ds, ax
    mov es, ax
    mov ss, ax
    mov sp, 0xFFFE

    cli
    cld
    sti

    ; Call C main function
    call kernel_main

    ; Should never reach here
    cli
    hlt

; BIOS interface functions
global clear_screen
clear_screen:
    push bp
    mov bp, sp
    push ax

    mov ah, 0x00
    mov al, 0x03
    int 0x10

    pop ax
    pop bp
    ret

global print_string
print_string:
    push bp
    mov bp, sp
    push ax
    push si

    mov si, [bp+4]   ; Get string pointer
.loop:
    lodsb
    test al, al
    jz .done
    mov ah, 0x0E
    int 0x10
    jmp .loop
.done:
    pop si
    pop ax
    pop bp
    ret

global print_char
print_char:
    push bp
    mov bp, sp
    push ax

    mov al, [bp+4]   ; Get character parameter
    mov ah, 0x0E
    int 0x10

    pop ax
    pop bp
    ret

global print_hex_word
print_hex_word:
    push bp
    mov bp, sp
    push ax
    push bx
    push cx

    mov ax, [bp+4]   ; Get word parameter
    mov bx, ax
    mov cl, 4

    ; Print high byte
    mov al, bh
    shr al, cl
    call print_hex_digit
    mov al, bh
    and al, 0x0F
    call print_hex_digit

    ; Print low byte
    mov al, bl
    shr al, cl
    call print_hex_digit
    mov al, bl
    and al, 0x0F
    call print_hex_digit

    pop cx
    pop bx
    pop ax
    pop bp
    ret

print_hex_digit:
    and al, 0x0F
    cmp al, 9
    jbe .digit
    add al, 'A' - '0' - 10
.digit:
    add al, '0'
    mov ah, 0x0E
    int 0x10
    ret

global get_keystroke
get_keystroke:
    push bp
    mov bp, sp

    mov ah, 0x00
    int 0x16
    ; AL contains the character

    pop bp
    ret

global read_command
read_command:
    push bp
    mov bp, sp
    push ax
    push bx
    push cx
    push di

    mov di, [bp+4]   ; Buffer pointer
    mov cx, [bp+6]   ; Max length
    xor bx, bx       ; Character counter

.read_loop:
    mov ah, 0x00
    int 0x16         ; Get keystroke

    cmp al, 0x0D     ; ENTER?
    je .done
    cmp al, 0x08     ; BACKSPACE?
    je .handle_backspace

    cmp al, 32       ; Printable?
    jb .read_loop
    cmp al, 126
    ja .read_loop

    cmp bx, cx       ; Room left?
    jae .read_loop

    mov [di], al     ; Store character
    inc di
    inc bx

    mov ah, 0x0E     ; Echo character
    int 0x10
    jmp .read_loop

.handle_backspace:
    cmp bx, 0
    je .read_loop

    dec di
    dec bx

    mov ah, 0x0E
    mov al, 0x08     ; Backspace
    int 0x10
    mov al, ' '      ; Space
    int 0x10
    mov al, 0x08     ; Backspace again
    int 0x10
    jmp .read_loop

.done:
    mov byte [di], 0 ; Null terminate

    mov ah, 0x0E     ; Print newline
    mov al, 0x0D
    int 0x10
    mov al, 0x0A
    int 0x10

    pop di
    pop cx
    pop bx
    pop ax
    pop bp
    ret

global reboot_system
reboot_system:
    push bp
    mov bp, sp

    ; Reboot via keyboard controller
    mov al, 0xFE
    out 0x64, al

    ; Fallback: triple fault
    int 3
    jmp $

; Pad to fill exactly three 512-byte sectors (1536 bytes)
times 1536-($-$$) db 0
