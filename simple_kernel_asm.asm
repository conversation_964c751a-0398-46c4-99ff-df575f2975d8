; Assembly stub and BIOS interface for simple_kernel.c
[BITS 16]

; External C function
extern kernel_main

; Entry point
global _start
_start:
    ; Segment setup
    mov ax, 0x8000
    mov ds, ax
    mov es, ax
    mov ss, ax
    mov sp, 0xFFFE

    cli
    cld
    sti

    ; Call C main function
    call kernel_main

    ; Should never reach here
    cli
    hlt

; BIOS interface functions
global clear_screen
clear_screen:
    push bp
    mov bp, sp
    push ax

    mov ah, 0x00
    mov al, 0x03
    int 0x10

    pop ax
    pop bp
    ret

global print_string
print_string:
    push bp
    mov bp, sp
    push ax
    push si

    mov si, [bp+4]   ; Get string pointer
.loop:
    lodsb
    test al, al
    jz .done
    mov ah, 0x0E
    int 0x10
    jmp .loop
.done:
    pop si
    pop ax
    pop bp
    ret

global print_char
print_char:
    push bp
    mov bp, sp
    push ax

    mov al, [bp+4]   ; Get character parameter
    mov ah, 0x0E
    int 0x10

    pop ax
    pop bp
    ret

global get_keystroke
get_keystroke:
    push bp
    mov bp, sp

    mov ah, 0x00
    int 0x16
    ; AL contains the character

    pop bp
    ret

global reboot_system
reboot_system:
    push bp
    mov bp, sp

    ; Reboot via keyboard controller
    mov al, 0xFE
    out 0x64, al

    ; Fallback: triple fault
    int 3
    jmp $

; Pad to fill exactly three 512-byte sectors (1536 bytes)
times 1536-($-$$) db 0
